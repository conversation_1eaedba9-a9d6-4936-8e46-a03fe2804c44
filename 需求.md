你是一名具有20年经验的产品经理和精通Python语言的工程师。请使用Python开发一个订单地址批量修改工具，具体要求如下：  
  
# 一、UI界面设计要求  
  
## （一）整体布局  
- 窗口形状：长方形  （1000p×600px）
- 设计风格：简约现代风格  
- 布局结构：上下分区布局  
- 上部分（10%）：功能按钮区域  
- 下部分（90%）：数据展示区域  
  
## （二）功能按钮区域设计  
- 按钮排列：水平排列，间距均匀  
- 按钮列表：登录、导入订单号、导入地址、开始修改、停止修改、导出数据、解析地址  
- 按钮状态：根据状态动态启用/禁用  
- 视觉样式：简洁直观，易于识别  

## （三）数据展示区域设计  
- 表格布局：3列表格，列标题为"订单号"、"地址"、"目前状态"
- 视觉设计：  
- 表头：深蓝色字体，白色背景  
- 网格线：浅灰色细线  
- 对齐方式：居中对齐  
- 状态区域：添加边框和背景色突出显示  
  
# 二、核心功能实现  
  
## （一）登录管理  
- 启动检测：程序启动时检查cookies.txt文件是否存在有效cookies  
- 登录流程：点击"登录"按钮调用cookie_exporter.py获取cookies 
- 权限控制：软件打开时检测是否有Cookies，没有Cookies的情况下，禁用所有操作按钮（除登录外）  
  
## （二）数据导入功能  
- 导入订单号：点击按钮后自动读取剪贴板内容作为订单号  
- 导入地址：点击按钮后自动读取剪贴板内容作为地址信息  
  
## （三）地址修改核心流程  
点击"开始修改"按钮后，对每个订单执行以下4个阶段：  
  
### 阶段1：地址解析请求准备  
- 输入：数据展示区域中的地址文本  
- 处理：根据`地址.md`文档格式生成JSON请求载荷  
- 输出：符合API要求的请求数据  
  
### 阶段2：地址智能解析  
请求网址:https://s.kwaixiaodian.com/rest/pc/scm/api/address/intelligent/parsing
请求方法;POST
状态代码;200 OK
远程地址;**************:443
引荐来源网址政策;no-referrer-when-downgrade
access-control-allow-credentials:true
access-control-allow-origin;https://s.kwaixiaodian.com
content-encoding;br
content-type;application/json;charset=UTF-8
date;Fri, 01 Aug 2025 02:06:46 GMT
server;Tengine
x-kslogid;1754014006.153
:authority;s.kwaixiaodian.com
:method;POST
:path;/rest/pc/scm/api/address/intelligent/parsing
:scheme;https
accept;application/json;charset=UTF-8
accept-encoding;gzip, deflate, br, zstd
accept-language;zh-CN,zh;q=0.9
content-length;109
content-type;application/json;charset=UTF-8
cookie;[从cookies.txt读取]
kpn;KWAIXIAODIAN
ks-csrf-token;390CED819FAD218D7987E3B6C313A086
ktrace-str;3|My40NTgzNjk4Mjg2NzM2NzY5LjIyMjg3NzY3LjE3NTQwMTQwMDU5NjIuMTAxMw==|My40NTgzNjk4Mjg2NzM2NzY5LjQzNDczNTg5LjE3NTQwMTQwMDU5NjIuMTAxMg==|0|plateco-kfx-service|plateco|true|src:Js,seqn:2749,rsi:115356d5-6fd3-4c27-b53a-3f84ebd6c70a,path:/zone/order/detail,rpi:ded83dcdc4
merchantsessionkey;1754013981555_60dc1548f657938e37cf454581eef589
origin;https://s.kwaixiaodian.com
priority;u=1, i
referer;https://s.kwaixiaodian.com/zone/order/detail?entranceScene=1&id=2521202088672119
sec-ch-ua;"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile;?0
sec-ch-ua-platform;"Windows"
sec-fetch-dest;empty
sec-fetch-mode;cors
sec-fetch-site;same-origin
trace-id;1.0.0.1754014005961.7
user-agent;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/5
- 响应处理：解析返回的地址结构化数据，响应格式在地址.md
  
### 阶段3：更新请求载荷生成  
- 输入：阶段2的响应数据  
- 处理：根据`地址2.md`文档格式生成更新请求载荷  
- 输出：符合更新API要求的JSON数据  
### 阶段4：地址更新执行  
请求网址：https://s.kwaixiaodian.com/rest/app/tts/ks/seller/order/address/update
请求方法：POST
状态代码：200 OK
远程地址：**************:443
引荐来源网址政策：no-referrer-when-downgrade
content-encodingcontent-encoding：br
content-type：application/json;charset=UTF-8
date：Fri, 01 Aug 2025 02:25:55 GMT
server：Tengine
x-kslogid：1754015154.917
x-request-id：754015154917541887br
content-type：application/json;charset=UTF-8
date：Fri, 01 Aug 2025 02:25:55 GMT
server：Tengine
x-kslogid：1754015154.917
x-request-id：754015154917541887
:authority：s.kwaixiaodian.com
:method：POST
:path：/rest/app/tts/ks/seller/order/address/update
:scheme：https
accept：application/json;charset=UTF-8
accept-encoding：gzip, deflate, br, zstd
accept-language：zh-CN,zh;q=0.9
content-length：374
content-type：application/json;charset=UTF-8
cookie：[从cookies.txt读取]
kpf：PC_WEB
kpn：KWAIXIAODIAN
ks-csrf-token：390CED819FAD218D7987E3B6C313A086
ktrace-str：3|My40NTgzNjk4Mjg2NzM2NzY5LjczNjc0MzY4LjE3NTQwMTUxNTQzODguMTAzOQ==|My40NTgzNjk4Mjg2NzM2NzY5LjIzNTk3ODI3LjE3NTQwMTUxNTQzODcuMTAzOA==|0|plateco-kfx-service|plateco|true|src:Js,seqn:2749,rsi:115356d5-6fd3-4c27-b53a-3f84ebd6c70a,path:/zone/order/detail,rpi:ded83dcdc4
merchantsessionkey：1754013981555_60dc1548f657938e37cf454581eef589
origin：https://s.kwaixiaodian.com
priority：u=1, i
referer：https://s.kwaixiaodian.com/zone/order/detail?entranceScene=1&id=2521202088672119
sec-ch-ua："Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile：?0
sec-ch-ua-platform："Windows"
sec-fetch-dest：empty
sec-fetch-mode：cors
sec-fetch-site：same-origin
trace-id：1.0.0.1754015154386.19
user-agent：Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

### 阶段5：状态更新  
- 成功响应：在"目前状态"列显示"修改成功"  
- 失败处理：显示具体错误信息  
- UI更新：实时更新表格中的状态信息  

| 响应字段    | 展示名称 | 数据类型 | 展示要求        |
| ------- | ---- | ---- | ----------- |
| message | 修改成功 | 文字   | 目前状态列显示修改成功 |

## （四）辅助功能  
- 停止修改：中断正在进行的批量修改操作  
- 导出数据：将当前数据表格导出为文件，文件命名格式：年-月-日-时-分-秒
- 解析地址：单独测试地址解析功能  
  
# 三、技术实现要求  
- 实现异步请求处理，避免界面卡顿  
- 添加错误处理和日志记录  
- 确保网络请求的稳定性和重试机制  
- 实现数据持久化存储