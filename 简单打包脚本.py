#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的打包脚本 - 直接使用PyInstaller命令行
避免spec文件的复杂性
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller 已安装")
        return True
    except ImportError:
        print("❌ PyInstaller 未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装 PyInstaller...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                      check=True, encoding='utf-8')
        print("✅ PyInstaller 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller 安装失败: {e}")
        return False

def build_exe_simple():
    """使用简单的命令行方式构建EXE"""
    print("开始构建EXE文件...")
    
    # 设置环境变量防止中文乱码
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    
    try:
        # 直接使用PyInstaller命令行
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",                    # 打包成单个文件
            "--windowed",                   # 无控制台窗口
            "--name=订单地址批量修改工具",    # 指定输出文件名
            "--exclude-module=PyQt6",       # 排除PyQt6
            "--exclude-module=PySide2",     # 排除PySide2
            "--exclude-module=PySide6",     # 排除PySide6
            "--exclude-module=tkinter",     # 排除tkinter
            "--hidden-import=PyQt5.QtCore",
            "--hidden-import=PyQt5.QtGui",
            "--hidden-import=PyQt5.QtWidgets",
            "--hidden-import=PyQt5.QtWebEngineWidgets",
            "--hidden-import=PyQt5.QtWebEngineCore",
            "--hidden-import=requests",
            "--hidden-import=pyperclip",
            "--hidden-import=openpyxl",
            "--hidden-import=cookie_exporter",
            "--hidden-import=excel_exporter",
            "--clean",                      # 清理临时文件
            "--noconfirm",                  # 不询问确认
            "main.py"                       # 主程序文件
        ]
        
        print(f"执行命令: {' '.join(cmd[:5])} ... (省略部分参数)")
        print("正在构建，请稍候...")
        
        result = subprocess.run(
            cmd,
            env=env,
            cwd=os.getcwd(),
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            print("✅ EXE文件构建成功")
            return True
        else:
            print(f"❌ 构建失败:")
            # 显示错误信息
            stderr_lines = result.stderr.split('\n')
            error_lines = [line for line in stderr_lines if 'ERROR:' in line or 'CRITICAL:' in line or 'ModuleNotFoundError' in line]
            if error_lines:
                print("关键错误信息:")
                for line in error_lines:
                    print(f"  {line}")
            else:
                print("最后的输出信息:")
                print(result.stderr[-800:])  # 显示最后800个字符
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出现错误: {e}")
        return False

def cleanup_build_files():
    """清理构建文件"""
    print("清理构建文件...")
    
    cleanup_dirs = ['build', '__pycache__']
    cleanup_files = ['订单地址批量修改工具.spec']
    
    for dir_name in cleanup_dirs:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"✅ 已删除 {dir_name} 目录")
    
    for file_name in cleanup_files:
        if Path(file_name).exists():
            os.remove(file_name)
            print(f"✅ 已删除 {file_name} 文件")

def check_required_files():
    """检查必需的文件"""
    required_files = [
        'main.py',
        'cookie_exporter.py', 
        'excel_exporter.py',
        'requirements.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必需文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必需文件都存在")
    return True

def main():
    """主函数"""
    print("🚀 订单地址批量修改工具简单打包脚本")
    print("=" * 50)
    print(f"Python 版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print("=" * 50)
    
    # 检查必需文件
    if not check_required_files():
        print("请确保所有必需文件都在当前目录中")
        return
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("无法安装PyInstaller，请手动安装: pip install pyinstaller")
            return
    
    # 构建EXE
    if build_exe_simple():
        print("\n🎉 打包完成!")
        print("📁 EXE文件位置: dist/订单地址批量修改工具.exe")
        
        # 检查输出文件
        exe_path = Path("dist/订单地址批量修改工具.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"📊 文件大小: {size_mb:.1f} MB")
        
        print("\n💡 使用说明:")
        print("1. 运行 dist/订单地址批量修改工具.exe")
        print("2. 首次使用请点击'登录'按钮获取Cookie")
        print("3. 按顺序导入订单号和地址，然后开始修改")
        
        # 询问是否清理构建文件
        try:
            choice = input("\n是否清理构建文件? (y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                cleanup_build_files()
        except KeyboardInterrupt:
            print("\n用户取消操作")
    else:
        print("\n❌ 打包失败，请检查错误信息")
        print("\n💡 如果遇到PyQt冲突问题，请尝试:")
        print("1. pip uninstall PyQt6 PySide2 PySide6")
        print("2. 重新运行此脚本")

if __name__ == "__main__":
    main()
