# 订单号显示问题说明

## 问题描述

当使用Excel打开CSV文件时，长数字（如订单号）会被自动转换为科学计数法显示。

**示例：**
- 原始订单号：`2521100324468078`
- Excel中显示：`2.5211E+15`

## 问题原因

Excel会自动识别数字格式，对于超过15位的数字，会自动转换为科学计数法显示，并且可能丢失精度。

## 解决方案

我们提供了3种导出格式来解决这个问题：

### 1. Excel文件格式 (推荐) ⭐

**优点：**
- ✅ 订单号完美显示，不会变成科学计数法
- ✅ 支持格式化（颜色、字体等）
- ✅ 列宽自动调整
- ✅ 状态列有颜色标识

**使用方法：**
- 在程序中点击"导出数据"
- 选择"Excel文件 (推荐)"
- 生成的`.xlsx`文件可以直接用Excel打开

**要求：**
- 需要安装openpyxl库：`pip install openpyxl`

### 2. CSV-Excel兼容格式

**优点：**
- ✅ 在Excel中订单号显示为文本
- ✅ 兼容性好，任何系统都能打开
- ✅ 文件体积小

**原理：**
- 在订单号前添加单引号 `'`
- Excel会将其识别为文本格式

**使用方法：**
- 在程序中点击"导出数据"
- 选择"CSV-Excel兼容"

### 3. CSV-标准格式

**特点：**
- 📄 标准CSV格式
- ⚠️ Excel打开时订单号会显示为科学计数法
- ✅ 用文本编辑器查看正常

**适用场景：**
- 需要标准CSV格式
- 用其他软件处理数据
- 用文本编辑器查看

## 推荐使用方式

1. **日常使用**：选择"Excel文件"格式
2. **需要兼容性**：选择"CSV-Excel兼容"格式
3. **数据处理**：选择"CSV-标准"格式

## 验证方法

导出后可以通过以下方式验证：

1. **Excel文件**：直接用Excel打开，订单号应该显示完整
2. **CSV文件**：用Excel打开，检查订单号是否为文本格式
3. **文本编辑器**：用记事本等打开，查看原始数据

## 技术说明

### Excel文件格式
- 使用openpyxl库生成真正的Excel文件
- 订单号列设置为文本格式：`number_format = '@'`
- 支持样式和格式化

### CSV兼容格式
- 在订单号前添加单引号：`'2521100324468078`
- Excel会自动识别为文本格式
- 保持CSV的兼容性

### 文件命名规则
- Excel格式：`订单地址修改结果_YYYY-MM-DD-HH-MM-SS.xlsx`
- CSV兼容：`订单地址修改结果_YYYY-MM-DD-HH-MM-SS_Excel兼容.csv`
- CSV标准：`订单地址修改结果_YYYY-MM-DD-HH-MM-SS_标准.csv`

## 常见问题

**Q: 为什么Excel会把订单号变成科学计数法？**
A: Excel对超过15位的数字会自动使用科学计数法显示，这是Excel的默认行为。

**Q: 单引号会不会影响数据？**
A: 单引号只是告诉Excel这是文本，不会影响实际数据内容。

**Q: 如果没有安装openpyxl怎么办？**
A: 程序会自动降级到CSV格式，或者运行 `pip install openpyxl` 安装。

**Q: 哪种格式最好？**
A: 推荐使用Excel格式，显示效果最好，功能最完整。
