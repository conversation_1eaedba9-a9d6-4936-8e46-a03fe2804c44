#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单地址批量修改工具
主应用程序
"""

import sys
import json
import logging
import pyperclip
import csv
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional

try:
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
        QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
        QMessageBox, QProgressBar, QLabel, QSplitter, QFrame
    )
    from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
    from PyQt5.QtGui import QFont, QPalette, QColor
    PYQT_VERSION = 5
except ImportError:
    try:
        from PyQt6.QtWidgets import (
            QApplication, QMainWindow, QVBoxLayout, QH<PERSON><PERSON>Layout, QWidget,
            Q<PERSON><PERSON><PERSON>utton, QT<PERSON>Widget, QTable<PERSON>idgetI<PERSON>, QHeaderView,
            QMessageBox, QProgressBar, QLabel, QSplitter, QFrame
        )
        from PyQt6.QtCore import QThread, pyqtSignal, Qt, QTimer
        from PyQt6.QtGui import QFont, QPalette, QColor
        PYQT_VERSION = 6
    except ImportError:
        print("错误：需要安装 PyQt5 或 PyQt6")
        sys.exit(1)


class OrderAddressModifier(QMainWindow):
    """订单地址批量修改工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.cookies = None
        self.is_logged_in = False
        self.is_modifying = False
        self.orders_data = []  # 存储订单数据
        
        # 设置日志
        self.setup_logging()
        
        # 初始化界面
        self.init_ui()
        
        # 检查登录状态
        self.check_login_status()

        # 加载保存的数据
        self.load_data()
        
    def setup_logging(self):
        """设置日志记录"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f"order_modifier_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)

    def extract_csrf_token(self):
        """从cookies中提取CSRF token"""
        if not self.cookies:
            return "390CED819FAD218D7987E3B6C313A086"  # 默认值

        # 查找ks-csrf-token（支持大小写）
        for cookie_pair in self.cookies.split(';'):
            cookie_pair = cookie_pair.strip()
            if cookie_pair.lower().startswith('ks-csrf-token='):
                return cookie_pair.split('=', 1)[1]

        return "390CED819FAD218D7987E3B6C313A086"  # 默认值

    def extract_merchant_session_key(self):
        """从cookies中提取merchant session key"""
        if not self.cookies:
            return "1754013981555_60dc1548f657938e37cf454581eef589"  # 默认值

        # 查找merchantsessionkey或merchantSessionKey（支持大小写）
        for cookie_pair in self.cookies.split(';'):
            cookie_pair = cookie_pair.strip()
            if cookie_pair.lower().startswith('merchantsessionkey='):
                return cookie_pair.split('=', 1)[1]

        return "1754013981555_60dc1548f657938e37cf454581eef589"  # 默认值
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("订单地址批量修改工具")
        self.setGeometry(100, 100, 1000, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建功能按钮区域（占10%高度）
        self.create_button_area(main_layout)
        
        # 创建数据展示区域（占90%高度）
        self.create_data_area(main_layout)
        
        # 设置布局比例
        main_layout.setStretchFactor(self.button_frame, 1)  # 10%
        main_layout.setStretchFactor(self.data_frame, 9)    # 90%
        
    def create_button_area(self, parent_layout):
        """创建功能按钮区域"""
        # 创建按钮区域框架
        self.button_frame = QFrame()
        self.button_frame.setFrameStyle(QFrame.Box)
        self.button_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        
        # 创建按钮布局
        button_layout = QHBoxLayout(self.button_frame)
        button_layout.setContentsMargins(10, 5, 10, 5)
        button_layout.setSpacing(10)
        
        # 创建功能按钮
        self.create_buttons(button_layout)
        
        parent_layout.addWidget(self.button_frame)
        
    def create_buttons(self, layout):
        """创建所有功能按钮"""
        # 按钮样式
        button_style = """
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 80px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """
        
        # 登录按钮
        self.login_btn = QPushButton("登录")
        self.login_btn.setStyleSheet(button_style)
        self.login_btn.clicked.connect(self.login)
        
        # 导入订单号按钮
        self.import_orders_btn = QPushButton("导入订单号")
        self.import_orders_btn.setStyleSheet(button_style)
        self.import_orders_btn.clicked.connect(self.import_orders)
        
        # 导入地址按钮
        self.import_addresses_btn = QPushButton("导入地址")
        self.import_addresses_btn.setStyleSheet(button_style)
        self.import_addresses_btn.clicked.connect(self.import_addresses)
        
        # 开始修改按钮
        self.start_modify_btn = QPushButton("开始修改")
        self.start_modify_btn.setStyleSheet(button_style.replace("#007bff", "#28a745").replace("#0056b3", "#1e7e34"))
        self.start_modify_btn.clicked.connect(self.start_modify)
        
        # 停止修改按钮
        self.stop_modify_btn = QPushButton("停止修改")
        self.stop_modify_btn.setStyleSheet(button_style.replace("#007bff", "#dc3545").replace("#0056b3", "#c82333"))
        self.stop_modify_btn.clicked.connect(self.stop_modify)
        
        # 导出数据按钮
        self.export_data_btn = QPushButton("导出数据")
        self.export_data_btn.setStyleSheet(button_style.replace("#007bff", "#17a2b8").replace("#0056b3", "#138496"))
        self.export_data_btn.clicked.connect(self.export_data)
        
        # 解析地址按钮
        self.parse_address_btn = QPushButton("解析地址")
        self.parse_address_btn.setStyleSheet(button_style.replace("#007bff", "#ffc107").replace("#0056b3", "#e0a800"))
        self.parse_address_btn.clicked.connect(self.parse_address)
        
        # 添加按钮到布局
        layout.addWidget(self.login_btn)
        layout.addWidget(self.import_orders_btn)
        layout.addWidget(self.import_addresses_btn)
        layout.addWidget(self.start_modify_btn)
        layout.addWidget(self.stop_modify_btn)
        layout.addWidget(self.export_data_btn)
        layout.addWidget(self.parse_address_btn)
        layout.addStretch()  # 添加弹性空间
        
    def create_data_area(self, parent_layout):
        """创建数据展示区域"""
        # 创建数据区域框架
        self.data_frame = QFrame()
        self.data_frame.setFrameStyle(QFrame.Box)
        self.data_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        
        # 创建数据区域布局
        data_layout = QVBoxLayout(self.data_frame)
        data_layout.setContentsMargins(10, 10, 10, 10)
        data_layout.setSpacing(10)
        
        # 创建表格
        self.create_table(data_layout)
        
        parent_layout.addWidget(self.data_frame)
        
    def create_table(self, layout):
        """创建数据展示表格"""
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["订单号", "地址", "目前状态"])
        
        # 设置表格样式
        self.table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d3d3d3;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #007bff;
                border: 1px solid #dee2e6;
            }
            QHeaderView::section {
                background-color: #1e3a8a;
                color: white;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                text-align: center;
            }
            QTableWidget::item {
                padding: 8px;
                text-align: center;
                border-bottom: 1px solid #d3d3d3;
            }
        """)
        
        # 设置表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.table)

    def check_login_status(self):
        """检查登录状态"""
        try:
            cookies_file = Path("data/cookies.txt")
            if cookies_file.exists():
                with open(cookies_file, 'r', encoding='utf-8') as f:
                    self.cookies = f.read().strip()
                if self.cookies:
                    self.is_logged_in = True
                    self.logger.info("检测到有效的cookies文件")
                else:
                    self.is_logged_in = False
                    self.logger.warning("cookies文件为空")
            else:
                self.is_logged_in = False
                self.logger.warning("未找到cookies文件")
        except Exception as e:
            self.is_logged_in = False
            self.logger.error(f"检查登录状态时出错: {e}")

        # 更新按钮状态
        self.update_button_states()

    def update_button_states(self):
        """更新按钮状态"""
        # 登录按钮始终可用
        self.login_btn.setEnabled(True)

        # 导入订单号按钮：需要登录且不在修改中
        self.import_orders_btn.setEnabled(self.is_logged_in and not self.is_modifying)

        # 导入地址按钮：需要登录、不在修改中、且已有订单数据
        has_orders = len(self.orders_data) > 0
        self.import_addresses_btn.setEnabled(self.is_logged_in and not self.is_modifying and has_orders)

        # 开始修改按钮：需要登录、不在修改中、有订单数据且有地址数据
        has_addresses = any(order.get('address', '').strip() for order in self.orders_data)
        self.start_modify_btn.setEnabled(self.is_logged_in and not self.is_modifying and has_orders and has_addresses)

        # 导出数据按钮：需要有数据
        self.export_data_btn.setEnabled(has_orders)

        # 解析地址按钮：需要登录且不在修改中
        self.parse_address_btn.setEnabled(self.is_logged_in and not self.is_modifying)

        # 停止修改按钮只在修改过程中可用
        self.stop_modify_btn.setEnabled(self.is_modifying)

    def login(self):
        """登录功能"""
        try:
            self.logger.info("开始登录流程")

            # 调用cookie_exporter.py
            import subprocess
            result = subprocess.run([sys.executable, "cookie_exporter.py"],
                                  capture_output=True, text=True, encoding='utf-8', errors='ignore')

            if result.returncode == 0:
                # 重新检查登录状态
                self.check_login_status()
                if self.is_logged_in:
                    QMessageBox.information(self, "登录成功", "Cookie获取成功，现在可以使用其他功能了！")
                    self.logger.info("登录成功")
                else:
                    QMessageBox.warning(self, "登录失败", "未能获取有效的Cookie，请重试")
                    self.logger.warning("登录失败：未获取到有效Cookie")
            else:
                QMessageBox.critical(self, "登录错误", f"Cookie获取程序执行失败：{result.stderr}")
                self.logger.error(f"Cookie获取程序执行失败：{result.stderr}")

        except Exception as e:
            QMessageBox.critical(self, "登录错误", f"登录过程中出现错误：{str(e)}")
            self.logger.error(f"登录过程中出现错误：{e}")

    def import_orders(self):
        """导入订单号"""
        try:
            # 从剪贴板读取内容
            clipboard_content = pyperclip.paste()
            if not clipboard_content.strip():
                QMessageBox.warning(self, "导入失败", "剪贴板内容为空，请先复制订单号")
                return

            # 解析订单号（假设每行一个订单号）
            order_lines = [line.strip() for line in clipboard_content.strip().split('\n') if line.strip()]

            if not order_lines:
                QMessageBox.warning(self, "导入失败", "未找到有效的订单号")
                return

            # 清空现有数据
            self.orders_data.clear()
            self.table.setRowCount(0)

            # 添加订单号到表格
            for order_id in order_lines:
                self.add_order_to_table(order_id, "", "待处理")

            QMessageBox.information(self, "导入成功", f"成功导入 {len(order_lines)} 个订单号")
            self.logger.info(f"成功导入 {len(order_lines)} 个订单号")

            # 自动保存数据
            self.save_data()

            # 更新按钮状态
            self.update_button_states()

        except Exception as e:
            QMessageBox.critical(self, "导入错误", f"导入订单号时出现错误：{str(e)}")
            self.logger.error(f"导入订单号时出现错误：{e}")

    def import_addresses(self):
        """导入地址"""
        try:
            # 从剪贴板读取内容
            clipboard_content = pyperclip.paste()
            if not clipboard_content.strip():
                QMessageBox.warning(self, "导入失败", "剪贴板内容为空，请先复制地址信息")
                return

            # 解析地址（假设每行一个地址）
            address_lines = [line.strip() for line in clipboard_content.strip().split('\n') if line.strip()]

            if not address_lines:
                QMessageBox.warning(self, "导入失败", "未找到有效的地址信息")
                return

            # 检查是否有订单数据
            if not self.orders_data:
                QMessageBox.warning(self, "导入失败", "请先导入订单号")
                return

            # 智能匹配地址数量：只导入与订单数量相等的地址
            orders_count = len(self.orders_data)
            addresses_to_import = address_lines[:orders_count]  # 只取前N个地址，N为订单数量

            # 将地址分配给订单（按顺序对应）
            for i, address in enumerate(addresses_to_import):
                self.orders_data[i]['address'] = address
                self.table.setItem(i, 1, QTableWidgetItem(address))
                self.table.setItem(i, 2, QTableWidgetItem("待修改"))

            # 提示信息
            imported_count = len(addresses_to_import)
            total_addresses = len(address_lines)

            if total_addresses > orders_count:
                message = f"成功导入 {imported_count} 个地址\n（剪贴板中有 {total_addresses} 个地址，已自动匹配订单数量）"
            else:
                message = f"成功导入 {imported_count} 个地址"

            QMessageBox.information(self, "导入成功", message)
            self.logger.info(f"成功导入 {imported_count} 个地址（剪贴板中共 {total_addresses} 个）")

            # 自动保存数据
            self.save_data()

            # 更新按钮状态
            self.update_button_states()

        except Exception as e:
            QMessageBox.critical(self, "导入错误", f"导入地址时出现错误：{str(e)}")
            self.logger.error(f"导入地址时出现错误：{e}")

    def add_order_to_table(self, order_id: str, address: str, status: str):
        """添加订单到表格"""
        row_count = self.table.rowCount()
        self.table.insertRow(row_count)

        # 添加数据到表格
        self.table.setItem(row_count, 0, QTableWidgetItem(order_id))
        self.table.setItem(row_count, 1, QTableWidgetItem(address))
        self.table.setItem(row_count, 2, QTableWidgetItem(status))

        # 设置居中对齐
        for col in range(3):
            item = self.table.item(row_count, col)
            if item:
                item.setTextAlignment(Qt.AlignCenter)

        # 添加到数据列表
        self.orders_data.append({
            'order_id': order_id,
            'address': address,
            'status': status
        })

    def start_modify(self):
        """开始修改"""
        try:
            # 检查是否有数据
            if not self.orders_data:
                QMessageBox.warning(self, "修改失败", "没有订单数据，请先导入订单号和地址")
                return

            # 检查是否有地址数据
            has_address = any(order.get('address', '').strip() for order in self.orders_data)
            if not has_address:
                QMessageBox.warning(self, "修改失败", "没有地址数据，请先导入地址")
                return

            # 确认开始修改
            reply = QMessageBox.question(self, "确认修改",
                                       f"即将开始修改 {len(self.orders_data)} 个订单的地址，是否继续？",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply != QMessageBox.Yes:
                return

            # 设置修改状态
            self.is_modifying = True
            self.update_button_states()

            # 创建批量修改工作线程
            csrf_token = self.extract_csrf_token()
            merchant_key = self.extract_merchant_session_key()
            self.modify_worker = BatchModifyWorker(self.orders_data, self.cookies, csrf_token, merchant_key)
            self.modify_worker.progress.connect(self.on_modify_progress)
            self.modify_worker.finished.connect(self.on_modify_finished)
            self.modify_worker.error.connect(self.on_modify_error)
            self.modify_worker.start()

            self.logger.info(f"开始批量修改 {len(self.orders_data)} 个订单")

        except Exception as e:
            QMessageBox.critical(self, "修改错误", f"开始修改时出现错误：{str(e)}")
            self.logger.error(f"开始修改时出现错误：{e}")
            self.is_modifying = False
            self.update_button_states()

    def on_modify_progress(self, index, order_id, status):
        """修改进度更新"""
        try:
            # 更新表格中的状态
            if index < self.table.rowCount():
                self.table.setItem(index, 2, QTableWidgetItem(status))
                # 设置居中对齐
                item = self.table.item(index, 2)
                if item:
                    item.setTextAlignment(Qt.AlignCenter)

                    # 根据状态设置颜色
                    if "成功" in status:
                        item.setBackground(QColor(212, 237, 218))  # 绿色背景
                    elif "失败" in status or "错误" in status:
                        item.setBackground(QColor(248, 215, 218))  # 红色背景
                    elif "正在" in status:
                        item.setBackground(QColor(255, 243, 205))  # 黄色背景

            # 更新数据
            if index < len(self.orders_data):
                self.orders_data[index]['status'] = status

            self.logger.info(f"订单 {order_id} 状态更新：{status}")

            # 自动保存数据
            self.save_data()

        except Exception as e:
            self.logger.error(f"更新进度时出现错误：{e}")

    def on_modify_finished(self):
        """修改完成"""
        self.is_modifying = False
        self.update_button_states()

        # 统计结果
        success_count = sum(1 for order in self.orders_data if "成功" in order.get('status', ''))
        total_count = len(self.orders_data)

        QMessageBox.information(self, "修改完成",
                              f"批量修改完成！\n成功：{success_count}/{total_count}")
        self.logger.info(f"批量修改完成，成功：{success_count}/{total_count}")

    def on_modify_error(self, error_msg):
        """修改错误"""
        self.is_modifying = False
        self.update_button_states()
        QMessageBox.critical(self, "修改错误", f"批量修改出现错误：{error_msg}")
        self.logger.error(f"批量修改出现错误：{error_msg}")

    def stop_modify(self):
        """停止修改"""
        try:
            if hasattr(self, 'modify_worker') and self.modify_worker.isRunning():
                self.modify_worker.stop()
                self.modify_worker.wait(3000)  # 等待最多3秒

            self.is_modifying = False
            self.update_button_states()
            QMessageBox.information(self, "提示", "已停止修改")
            self.logger.info("用户停止了批量修改")

        except Exception as e:
            self.logger.error(f"停止修改时出现错误：{e}")
            self.is_modifying = False
            self.update_button_states()

    def export_data(self):
        """导出数据"""
        try:
            if not self.orders_data:
                QMessageBox.warning(self, "导出失败", "没有数据可以导出")
                return

            # 询问用户导出格式
            from PyQt5.QtWidgets import QMessageBox, QPushButton

            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("选择导出格式")
            msg_box.setText("请选择导出格式：")
            msg_box.setInformativeText("Excel格式可以完美保持订单号格式，CSV格式兼容性更好")

            excel_btn = msg_box.addButton("Excel文件 (推荐)", QMessageBox.YesRole)
            csv_excel_btn = msg_box.addButton("CSV-Excel兼容", QMessageBox.NoRole)
            csv_std_btn = msg_box.addButton("CSV-标准格式", QMessageBox.RejectRole)
            cancel_btn = msg_box.addButton("取消", QMessageBox.RejectRole)

            msg_box.exec_()
            clicked_button = msg_box.clickedButton()

            if clicked_button == cancel_btn:
                return

            # 生成文件名（按时间戳命名）
            timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")

            # 创建导出目录
            export_dir = Path("exports")
            export_dir.mkdir(exist_ok=True)

            if clicked_button == excel_btn:
                # Excel格式导出
                try:
                    from excel_exporter import export_to_excel
                    output_file = export_to_excel(self.orders_data)
                    success_msg = f"数据已导出到Excel文件：{output_file}\n\n✅ 订单号在Excel中会正确显示，不会变成科学计数法"
                    QMessageBox.information(self, "导出成功", success_msg)
                    self.logger.info(f"Excel导出成功：{output_file}")
                    return
                except ImportError:
                    QMessageBox.warning(self, "导出失败", "Excel导出需要安装openpyxl库\n\n请运行：pip install openpyxl\n\n将使用CSV格式导出")
                    clicked_button = csv_excel_btn  # 降级到CSV格式
                except Exception as e:
                    QMessageBox.critical(self, "导出错误", f"Excel导出失败：{str(e)}\n\n将使用CSV格式导出")
                    clicked_button = csv_excel_btn  # 降级到CSV格式

            if clicked_button == csv_excel_btn:
                # Excel兼容CSV格式
                filename = f"订单地址修改结果_{timestamp}_Excel兼容.csv"
                export_file = export_dir / filename

                with open(export_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # 写入表头
                    writer.writerow(['订单号', '地址', '目前状态'])

                    # 写入数据 - 使用单引号前缀强制Excel识别为文本
                    for order in self.orders_data:
                        order_id = order.get('order_id', '')
                        # 在订单号前加单引号，强制Excel识别为文本
                        if order_id:
                            order_id = f"'{order_id}"

                        writer.writerow([
                            order_id,
                            order.get('address', ''),
                            order.get('status', '')
                        ])

                success_msg = f"数据已导出到：{export_file}\n\n💡 提示：此格式在Excel中打开时订单号会正确显示为文本"

            else:  # csv_std_btn
                # 标准CSV格式
                filename = f"订单地址修改结果_{timestamp}_标准.csv"
                export_file = export_dir / filename

                with open(export_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # 写入表头
                    writer.writerow(['订单号', '地址', '目前状态'])

                    # 写入数据 - 原始格式
                    for order in self.orders_data:
                        writer.writerow([
                            order.get('order_id', ''),
                            order.get('address', ''),
                            order.get('status', '')
                        ])

                success_msg = f"数据已导出到：{export_file}\n\n⚠️ 注意：用Excel打开时订单号可能显示为科学计数法，建议用文本编辑器查看"

            QMessageBox.information(self, "导出成功", success_msg)
            self.logger.info(f"数据导出成功：{export_file}")

            # 导出完成后清理文件夹
            self.cleanup_after_export()

        except Exception as e:
            QMessageBox.critical(self, "导出错误", f"导出数据时出现错误：{str(e)}")
            self.logger.error(f"导出数据时出现错误：{e}")

    def cleanup_after_export(self):
        """导出完成后清理文件夹"""
        try:
            import shutil
            import os

            # 1. 删除Cookie导出工具文件夹
            cookie_tool_path = Path(os.path.expanduser("~")) / "AppData" / "Local" / "Cookie导出工具"
            if cookie_tool_path.exists():
                shutil.rmtree(cookie_tool_path)
                self.logger.info(f"已删除Cookie导出工具文件夹：{cookie_tool_path}")

            # 2. 删除根目录下的data文件夹
            data_path = Path("data")
            if data_path.exists():
                shutil.rmtree(data_path)
                self.logger.info(f"已删除data文件夹：{data_path}")

            # 3. 清空内存中的数据
            self.orders_data.clear()
            self.table.setRowCount(0)

            # 4. 重置登录状态
            self.cookies = None
            self.is_logged_in = False

            # 5. 更新按钮状态
            self.update_button_states()

            self.logger.info("导出后清理完成")

        except Exception as e:
            self.logger.error(f"清理文件夹时出现错误：{e}")
            # 清理失败不影响主要功能，只记录日志

    def save_data(self):
        """保存数据到文件"""
        try:
            data_dir = Path("data")
            data_dir.mkdir(exist_ok=True)

            data_file = data_dir / "orders_data.json"

            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(self.orders_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"数据已保存到：{data_file}")

        except Exception as e:
            self.logger.error(f"保存数据时出现错误：{e}")

    def load_data(self):
        """从文件加载数据"""
        try:
            data_file = Path("data/orders_data.json")

            if data_file.exists():
                with open(data_file, 'r', encoding='utf-8') as f:
                    self.orders_data = json.load(f)

                # 恢复表格数据
                self.table.setRowCount(0)
                for order in self.orders_data:
                    self.add_order_to_table_from_data(order)

                self.logger.info(f"已加载 {len(self.orders_data)} 条数据")

        except Exception as e:
            self.logger.error(f"加载数据时出现错误：{e}")
            self.orders_data = []

    def add_order_to_table_from_data(self, order_data):
        """从数据添加订单到表格"""
        row_count = self.table.rowCount()
        self.table.insertRow(row_count)

        # 添加数据到表格
        self.table.setItem(row_count, 0, QTableWidgetItem(order_data.get('order_id', '')))
        self.table.setItem(row_count, 1, QTableWidgetItem(order_data.get('address', '')))
        self.table.setItem(row_count, 2, QTableWidgetItem(order_data.get('status', '')))

        # 设置居中对齐
        for col in range(3):
            item = self.table.item(row_count, col)
            if item:
                item.setTextAlignment(Qt.AlignCenter)

                # 根据状态设置颜色
                if col == 2:  # 状态列
                    status = order_data.get('status', '')
                    if "成功" in status:
                        item.setBackground(QColor(212, 237, 218))  # 绿色背景
                    elif "失败" in status or "错误" in status:
                        item.setBackground(QColor(248, 215, 218))  # 红色背景

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 保存数据
            self.save_data()

            # 停止正在运行的线程
            if hasattr(self, 'modify_worker') and self.modify_worker.isRunning():
                self.modify_worker.stop()
                self.modify_worker.wait(3000)

            self.logger.info("应用程序正常关闭")
            event.accept()

        except Exception as e:
            self.logger.error(f"关闭应用程序时出现错误：{e}")
            event.accept()

    def parse_address(self):
        """解析地址"""
        try:
            # 从剪贴板读取地址
            clipboard_content = pyperclip.paste()
            if not clipboard_content.strip():
                QMessageBox.warning(self, "解析失败", "剪贴板内容为空，请先复制要解析的地址")
                return

            # 创建解析工作线程
            csrf_token = self.extract_csrf_token()
            merchant_key = self.extract_merchant_session_key()
            self.parse_worker = AddressParseWorker(clipboard_content.strip(), self.cookies, csrf_token, merchant_key)
            self.parse_worker.finished.connect(self.on_parse_finished)
            self.parse_worker.error.connect(self.on_parse_error)
            self.parse_worker.start()

            # 显示进度
            QMessageBox.information(self, "提示", "正在解析地址，请稍候...")

        except Exception as e:
            QMessageBox.critical(self, "解析错误", f"解析地址时出现错误：{str(e)}")
            self.logger.error(f"解析地址时出现错误：{e}")

    def on_parse_finished(self, result):
        """地址解析完成"""
        if result.get('success'):
            data = result.get('data', {})
            suggest_result = data.get('suggestResult', {})

            # 格式化显示结果
            result_text = f"""解析结果：
收件人：{suggest_result.get('consignee', '')}
手机号：{suggest_result.get('mobile', '')}
省份：{suggest_result.get('province', '')} ({suggest_result.get('provinceCode', '')})
城市：{suggest_result.get('city', '')} ({suggest_result.get('cityCode', '')})
区县：{suggest_result.get('district', '')} ({suggest_result.get('districtCode', '')})
街道：{suggest_result.get('town', '')} ({suggest_result.get('townCode', '')})
详细地址：{suggest_result.get('address', '')}"""

            QMessageBox.information(self, "解析成功", result_text)
            self.logger.info("地址解析成功")
        else:
            QMessageBox.warning(self, "解析失败", f"地址解析失败：{result.get('error_msg', '未知错误')}")
            self.logger.warning(f"地址解析失败：{result.get('error_msg', '未知错误')}")

    def on_parse_error(self, error_msg):
        """地址解析错误"""
        QMessageBox.critical(self, "解析错误", f"地址解析出现错误：{error_msg}")
        self.logger.error(f"地址解析出现错误：{error_msg}")


class AddressParseWorker(QThread):
    """地址解析工作线程"""
    finished = pyqtSignal(dict)  # 解析结果
    error = pyqtSignal(str)      # 错误信息

    def __init__(self, address_text, cookies, csrf_token, merchant_key):
        super().__init__()
        self.address_text = address_text
        self.cookies = cookies
        self.csrf_token = csrf_token
        self.merchant_key = merchant_key

    def run(self):
        """执行地址解析"""
        try:
            import requests

            # 构建请求载荷
            payload = {
                "text": self.address_text
            }

            # 构建请求头
            headers = {
                'authority': 's.kwaixiaodian.com',
                'method': 'POST',
                'path': '/rest/pc/scm/api/address/intelligent/parsing',
                'scheme': 'https',
                'accept': 'application/json;charset=UTF-8',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9',
                'content-type': 'application/json;charset=UTF-8',
                'cookie': self.cookies,
                'kpn': 'KWAIXIAODIAN',
                'ks-csrf-token': self.csrf_token,
                'ktrace-str': '3|My40NTgzNjk4Mjg2NzM2NzY5LjIyMjg3NzY3LjE3NTQwMTQwMDU5NjIuMTAxMw==|My40NTgzNjk4Mjg2NzM2NzY5LjQzNDczNTg5LjE3NTQwMTQwMDU5NjIuMTAxMg==|0|plateco-kfx-service|plateco|true|src:Js,seqn:2749,rsi:115356d5-6fd3-4c27-b53a-3f84ebd6c70a,path:/zone/order/detail,rpi:ded83dcdc4',
                'merchantsessionkey': self.merchant_key,
                'origin': 'https://s.kwaixiaodian.com',
                'priority': 'u=1, i',
                'referer': 'https://s.kwaixiaodian.com/zone/order/detail?entranceScene=1&id=2521202088672119',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'trace-id': '1.0.0.1754014005961.7',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 发送请求
            url = "https://s.kwaixiaodian.com/rest/pc/scm/api/address/intelligent/parsing"
            response = requests.post(url, json=payload, headers=headers, timeout=30)

            if response.status_code == 200:
                result = response.json()
                self.finished.emit(result)
            else:
                self.error.emit(f"HTTP错误：{response.status_code}")

        except requests.exceptions.RequestException as e:
            self.error.emit(f"网络请求错误：{str(e)}")
        except Exception as e:
            self.error.emit(f"解析过程中出现错误：{str(e)}")


class AddressUpdateWorker(QThread):
    """地址更新工作线程"""
    finished = pyqtSignal(dict)  # 更新结果
    error = pyqtSignal(str)      # 错误信息

    def __init__(self, order_id, parsed_address_data, cookies, csrf_token):
        super().__init__()
        self.order_id = order_id
        self.parsed_address_data = parsed_address_data
        self.cookies = cookies
        self.csrf_token = csrf_token

    def run(self):
        """执行地址更新"""
        try:
            import requests

            # 从解析结果构建更新载荷
            suggest_result = self.parsed_address_data.get('data', {}).get('suggestResult', {})

            payload = {
                "data": {
                    "consignee": suggest_result.get('consignee', ''),
                    "mobile": suggest_result.get('mobile', ''),
                    "address": suggest_result.get('address', ''),
                    "provinceCode": suggest_result.get('provinceCode', 0),
                    "province": suggest_result.get('province', ''),
                    "cityCode": suggest_result.get('cityCode', 0),
                    "city": suggest_result.get('city', ''),
                    "districtCode": suggest_result.get('districtCode', 0),
                    "district": suggest_result.get('district', ''),
                    "townCode": suggest_result.get('townCode', 0),
                    "town": suggest_result.get('town', ''),
                    "partialUpdate": True,
                    "oid": int(self.order_id),
                    "orderId": "",
                    "source": 0,
                    "agreeType": "",
                    "needConfirm": True
                }
            }

            # 构建请求头
            headers = {
                'authority': 's.kwaixiaodian.com',
                'method': 'POST',
                'path': '/rest/app/tts/ks/seller/order/address/update',
                'scheme': 'https',
                'accept': 'application/json;charset=UTF-8',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9',
                'content-type': 'application/json;charset=UTF-8',
                'cookie': self.cookies,
                'kpf': 'PC_WEB',
                'kpn': 'KWAIXIAODIAN',
                'ks-csrf-token': self.csrf_token,
                'ktrace-str': '3|My40NTgzNjk4Mjg2NzM2NzY5LjczNjc0MzY4LjE3NTQwMTUxNTQzODguMTAzOQ==|My40NTgzNjk4Mjg2NzM2NzY5LjIzNTk3ODI3LjE3NTQwMTUxNTQzODcuMTAzOA==|0|plateco-kfx-service|plateco|true|src:Js,seqn:2749,rsi:115356d5-6fd3-4c27-b53a-3f84ebd6c70a,path:/zone/order/detail,rpi:ded83dcdc4',
                'merchantsessionkey': self.merchant_key,
                'origin': 'https://s.kwaixiaodian.com',
                'priority': 'u=1, i',
                'referer': 'https://s.kwaixiaodian.com/zone/order/detail?entranceScene=1&id=2521202088672119',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'trace-id': '1.0.0.1754015154386.19',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 发送请求
            url = "https://s.kwaixiaodian.com/rest/app/tts/ks/seller/order/address/update"
            response = requests.post(url, json=payload, headers=headers, timeout=30)

            if response.status_code == 200:
                result = response.json()
                self.finished.emit(result)
            else:
                self.error.emit(f"HTTP错误：{response.status_code}")

        except requests.exceptions.RequestException as e:
            self.error.emit(f"网络请求错误：{str(e)}")
        except Exception as e:
            self.error.emit(f"更新过程中出现错误：{str(e)}")


class BatchModifyWorker(QThread):
    """批量修改工作线程"""
    progress = pyqtSignal(int, str, str)  # 索引, 订单号, 状态信息
    finished = pyqtSignal()               # 完成信号
    error = pyqtSignal(str)               # 错误信息

    def __init__(self, orders_data, cookies, csrf_token, merchant_key):
        super().__init__()
        self.orders_data = orders_data
        self.cookies = cookies
        self.csrf_token = csrf_token
        self.merchant_key = merchant_key
        self.should_stop = False

    def stop(self):
        """停止修改"""
        self.should_stop = True

    def run(self):
        """执行批量修改"""
        try:
            import requests
            import time

            for i, order in enumerate(self.orders_data):
                if self.should_stop:
                    break

                order_id = order['order_id']
                address = order['address']

                if not address.strip():
                    self.progress.emit(i, order_id, "跳过：地址为空")
                    continue

                try:
                    # 阶段1：地址解析
                    self.progress.emit(i, order_id, "正在解析地址...")
                    parse_result = self.parse_address(address)

                    if not parse_result.get('success'):
                        self.progress.emit(i, order_id, f"解析失败：{parse_result.get('error_msg', '未知错误')}")
                        continue

                    # 阶段2：地址更新
                    self.progress.emit(i, order_id, "正在更新地址...")
                    update_result = self.update_address(order_id, parse_result)

                    # 详细的结果处理
                    if update_result.get('success'):
                        # 检查是否有成功消息
                        message = "修改成功"
                        if 'data' in update_result and 'data' in update_result['data']:
                            handler_data = update_result['data'].get('data', {})
                            if 'message' in handler_data:
                                message = handler_data['message']
                        self.progress.emit(i, order_id, message)
                    else:
                        error_msg = update_result.get('error_msg', '未知错误')
                        # 如果error_msg为空，尝试从其他字段获取错误信息
                        if not error_msg or error_msg == '未知错误':
                            if 'result' in update_result:
                                error_msg = f"API返回result: {update_result['result']}"
                            elif 'message' in update_result:
                                error_msg = f"API返回message: {update_result['message']}"
                            elif 'error_msg' in update_result:
                                error_msg = f"API返回error_msg: {update_result['error_msg']}"
                            else:
                                error_msg = f"API返回: {str(update_result)[:100]}"
                        self.progress.emit(i, order_id, f"更新失败：{error_msg}")

                except Exception as e:
                    self.progress.emit(i, order_id, f"处理错误：{str(e)}")

                # 添加延迟避免请求过快
                time.sleep(1)

            self.finished.emit()

        except Exception as e:
            self.error.emit(f"批量修改过程中出现错误：{str(e)}")

    def parse_address(self, address_text, max_retries=3):
        """解析地址（带重试机制）"""
        import requests

        for attempt in range(max_retries):
            try:
                payload = {"text": address_text}
                headers = self.get_parse_headers()
                url = "https://s.kwaixiaodian.com/rest/pc/scm/api/address/intelligent/parsing"

                response = requests.post(url, json=payload, headers=headers, timeout=30)

                if response.status_code == 200:
                    result = response.json()
                    # 检查API响应是否成功
                    if result.get('result') == 1 or result.get('success') == True:
                        return result
                    else:
                        # API返回了错误
                        error_msg = result.get('error_msg') or result.get('message') or f"解析失败，result: {result.get('result')}"
                        return {"success": False, "error_msg": error_msg, "raw_response": result}
                else:
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)  # 指数退避
                        continue
                    return {"success": False, "error_msg": f"HTTP错误：{response.status_code}，响应：{response.text[:200]}"}

            except requests.exceptions.RequestException as e:
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                return {"success": False, "error_msg": f"网络错误：{str(e)}"}
            except Exception as e:
                return {"success": False, "error_msg": f"解析错误：{str(e)}"}

    def update_address(self, order_id, parse_result, max_retries=3):
        """更新地址（带重试机制）"""
        import requests

        suggest_result = parse_result.get('data', {}).get('suggestResult', {})

        payload = {
            "data": {
                "consignee": suggest_result.get('consignee', ''),
                "mobile": suggest_result.get('mobile', ''),
                "address": suggest_result.get('address', ''),
                "provinceCode": suggest_result.get('provinceCode', 0),
                "province": suggest_result.get('province', ''),
                "cityCode": suggest_result.get('cityCode', 0),
                "city": suggest_result.get('city', ''),
                "districtCode": suggest_result.get('districtCode', 0),
                "district": suggest_result.get('district', ''),
                "townCode": suggest_result.get('townCode', 0),
                "town": suggest_result.get('town', ''),
                "partialUpdate": True,
                "oid": int(order_id),
                "orderId": "",
                "source": 0,
                "agreeType": "",
                "needConfirm": True
            }
        }

        for attempt in range(max_retries):
            try:
                headers = self.get_update_headers()
                url = "https://s.kwaixiaodian.com/rest/app/tts/ks/seller/order/address/update"

                response = requests.post(url, json=payload, headers=headers, timeout=30)

                if response.status_code == 200:
                    result = response.json()
                    # 检查API响应是否成功
                    if result.get('result') == 1 or result.get('success') == True:
                        return {"success": True, "data": result}
                    else:
                        # API返回了错误
                        error_msg = result.get('error_msg') or result.get('message') or f"API错误，result: {result.get('result')}"
                        return {"success": False, "error_msg": error_msg, "raw_response": result}
                else:
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)  # 指数退避
                        continue
                    return {"success": False, "error_msg": f"HTTP错误：{response.status_code}，响应：{response.text[:200]}"}

            except requests.exceptions.RequestException as e:
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                return {"success": False, "error_msg": f"网络错误：{str(e)}"}
            except Exception as e:
                return {"success": False, "error_msg": f"更新错误：{str(e)}"}

    def get_parse_headers(self):
        """获取解析请求头"""
        return {
            'authority': 's.kwaixiaodian.com',
            'method': 'POST',
            'path': '/rest/pc/scm/api/address/intelligent/parsing',
            'scheme': 'https',
            'accept': 'application/json;charset=UTF-8',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json;charset=UTF-8',
            'cookie': self.cookies,
            'kpn': 'KWAIXIAODIAN',
            'ks-csrf-token': self.csrf_token,
            'ktrace-str': '3|My40NTgzNjk4Mjg2NzM2NzY5LjIyMjg3NzY3LjE3NTQwMTQwMDU5NjIuMTAxMw==|My40NTgzNjk4Mjg2NzM2NzY5LjQzNDczNTg5LjE3NTQwMTQwMDU5NjIuMTAxMg==|0|plateco-kfx-service|plateco|true|src:Js,seqn:2749,rsi:115356d5-6fd3-4c27-b53a-3f84ebd6c70a,path:/zone/order/detail,rpi:ded83dcdc4',
            'merchantsessionkey': self.merchant_key,
            'origin': 'https://s.kwaixiaodian.com',
            'priority': 'u=1, i',
            'referer': 'https://s.kwaixiaodian.com/zone/order/detail?entranceScene=1&id=2521202088672119',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'trace-id': '1.0.0.1754014005961.7',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

    def get_update_headers(self):
        """获取更新请求头"""
        return {
            'authority': 's.kwaixiaodian.com',
            'method': 'POST',
            'path': '/rest/app/tts/ks/seller/order/address/update',
            'scheme': 'https',
            'accept': 'application/json;charset=UTF-8',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json;charset=UTF-8',
            'cookie': self.cookies,
            'kpf': 'PC_WEB',
            'kpn': 'KWAIXIAODIAN',
            'ks-csrf-token': self.csrf_token,
            'ktrace-str': '3|My40NTgzNjk4Mjg2NzM2NzY5LjczNjc0MzY4LjE3NTQwMTUxNTQzODguMTAzOQ==|My40NTgzNjk4Mjg2NzM2NzY5LjIzNTk3ODI3LjE3NTQwMTUxNTQzODcuMTAzOA==|0|plateco-kfx-service|plateco|true|src:Js,seqn:2749,rsi:115356d5-6fd3-4c27-b53a-3f84ebd6c70a,path:/zone/order/detail,rpi:ded83dcdc4',
            'merchantsessionkey': self.merchant_key,
            'origin': 'https://s.kwaixiaodian.com',
            'priority': 'u=1, i',
            'referer': 'https://s.kwaixiaodian.com/zone/order/detail?entranceScene=1&id=2521202088672119',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'trace-id': '1.0.0.1754015154386.19',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setApplicationName("订单地址批量修改工具")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = OrderAddressModifier()
    window.show()
    
    sys.exit(app.exec_() if PYQT_VERSION == 5 else app.exec())
