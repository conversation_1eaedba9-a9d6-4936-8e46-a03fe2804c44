# 订单地址批量修改工具

这是一个用于批量修改快手小店订单地址的Python工具，具有现代化的图形界面。

## 功能特性

- 🔐 **自动登录管理** - 集成cookie获取功能，自动管理登录状态
- 📋 **剪贴板导入** - 支持从剪贴板快速导入订单号和地址信息
- 🔄 **批量处理** - 支持批量修改多个订单的地址信息
- 🎯 **智能解析** - 自动解析地址信息，提取省市区街道等详细信息
- 📊 **实时状态** - 实时显示每个订单的处理状态和进度
- 💾 **数据持久化** - 自动保存和恢复工作数据
- 📤 **数据导出** - 支持将处理结果导出为CSV文件
- 🔄 **重试机制** - 内置网络请求重试机制，提高成功率
- 📝 **日志记录** - 完整的操作日志记录

## 安装要求

- Python 3.7+
- PyQt5
- requests
- pyperclip

## 安装步骤

1. 克隆或下载项目文件
2. 安装依赖包：
```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 启动程序
```bash
python main.py
```

### 2. 登录
- 点击"登录"按钮
- 程序会自动打开浏览器窗口
- 在浏览器中登录快手小店后台
- 等待cookie自动获取完成

### 3. 导入数据
- **导入订单号**：复制订单号到剪贴板（每行一个），点击"导入订单号"
- **导入地址**：复制地址信息到剪贴板（每行一个），点击"导入地址"
  - 🎯 智能匹配：自动匹配地址数量与订单数量
  - 📝 例：2个订单时，剪贴板有5个地址，只导入前2个

### 4. 开始修改
- 确认数据无误后，点击"开始修改"
- 程序会自动处理每个订单：
  1. 解析地址信息
  2. 更新订单地址
  3. 显示处理结果

### 5. 其他功能
- **停止修改**：中途停止批量处理
- **解析地址**：单独测试地址解析功能
- **导出数据**：支持3种格式导出
  - 📊 Excel文件（推荐）：订单号完美显示
  - 📄 CSV-Excel兼容：在Excel中正确显示
  - 📝 CSV-标准：标准格式，兼容性好
- **自动清理**：导出完成后自动清理临时文件

## 文件结构

```
订单地址批量修改工具/
├── main.py                 # 主程序文件
├── cookie_exporter.py      # Cookie获取工具
├── requirements.txt        # 依赖包列表
├── README.md              # 说明文档
├── 需求.md                # 需求文档
├── 地址.md                # 地址解析API文档
├── 地址2.md               # 地址更新API文档
├── data/                  # 数据目录
│   ├── cookies.txt        # 登录cookie
│   └── orders_data.json   # 订单数据
├── logs/                  # 日志目录
└── exports/               # 导出文件目录
```

## 注意事项

1. **网络连接**：确保网络连接稳定，程序需要访问快手小店API
2. **登录状态**：定期重新登录以保持cookie有效性
3. **数据备份**：重要数据建议手动备份
4. **批量限制**：建议分批处理大量订单，避免API限制

## 故障排除

### 常见问题

1. **登录失败**
   - 检查网络连接
   - 重新运行cookie_exporter.py
   - 确认快手小店账号状态正常

2. **地址解析失败**
   - 检查地址格式是否正确
   - 确认包含收件人、电话、详细地址信息

3. **修改失败 - CSRF token invalid**
   - ✅ 已修复：程序现在会自动从cookies中提取正确的CSRF token
   - ✅ 已修复：支持大小写不敏感的token提取
   - 如果仍然出现，请重新点击"登录"按钮获取新的cookies

4. **Unicode解码错误**
   - ✅ 已修复：程序已经修复了编码问题
   - ✅ 已修复：添加了错误忽略机制

5. **订单号在Excel中显示为科学计数法**
   - ✅ 已修复：程序现在提供3种导出格式
   - 推荐使用"Excel文件"格式，订单号会正确显示
   - 详细说明请查看《订单号显示问题说明.md》

6. **其他修改失败**
   - 检查订单状态是否允许修改
   - 确认登录状态有效
   - 查看日志文件获取详细错误信息

### 日志文件

程序会在`logs/`目录下生成日志文件，文件名格式为：
`order_modifier_YYYYMMDD.log`

## 快速测试

如果想验证程序是否正常工作，可以运行诊断工具：
```bash
python 诊断工具.py
```

该工具会检查：
- 依赖包安装情况
- 必要文件是否存在
- cookies文件是否有效
- CSRF token是否正确提取

## 技术支持

如遇到问题，请查看日志文件中的错误信息，或联系技术支持。
