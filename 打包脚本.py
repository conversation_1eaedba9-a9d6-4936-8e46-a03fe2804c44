#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单地址批量修改工具打包脚本
支持Python 3.13，防止中文乱码
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller 已安装")
        return True
    except ImportError:
        print("❌ PyInstaller 未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装 PyInstaller...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                      check=True, encoding='utf-8')
        print("✅ PyInstaller 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller 安装失败: {e}")
        return False

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'PyQt5.QtWebEngineWidgets',
        'PyQt5.QtWebEngineCore',
        'requests',
        'pyperclip',
        'openpyxl',
        'openpyxl.styles',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'json',
        'csv',
        'pathlib',
        'urllib.parse',
        'datetime',
        'logging',
        'time',
        'threading',
        'subprocess',
        'shutil',
        'cookie_exporter',
        'excel_exporter',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt6',
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'PyQt6.QtWebEngineWidgets',
        'PyQt6.QtWebEngineCore',
        'PySide2',
        'PySide6',
        'tkinter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='订单地址批量修改工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version_file=None,
)
'''
    
    with open('订单工具_完整版.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建 spec 文件")

def build_exe():
    """构建EXE文件"""
    print("开始构建EXE文件...")

    # 设置环境变量防止中文乱码
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'

    try:
        # 使用spec文件构建（排除模块已在spec文件中设置）
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "订单工具_完整版.spec"
        ]

        print(f"执行命令: {' '.join(cmd)}")
        print("正在构建，请稍候...")

        result = subprocess.run(
            cmd,
            env=env,
            cwd=os.getcwd(),
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='ignore'
        )

        if result.returncode == 0:
            print("✅ EXE文件构建成功")
            return True
        else:
            print(f"❌ 构建失败:")
            # 只显示关键错误信息
            stderr_lines = result.stderr.split('\n')
            error_lines = [line for line in stderr_lines if 'ERROR:' in line or 'CRITICAL:' in line]
            if error_lines:
                print("关键错误信息:")
                for line in error_lines:
                    print(f"  {line}")
            else:
                print("详细错误信息:")
                print(result.stderr[-1000:])  # 只显示最后1000个字符
            return False

    except Exception as e:
        print(f"❌ 构建过程中出现错误: {e}")
        return False

def cleanup_build_files():
    """清理构建文件"""
    print("清理构建文件...")
    
    cleanup_dirs = ['build', '__pycache__']
    cleanup_files = ['订单工具_完整版.spec']
    
    for dir_name in cleanup_dirs:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"✅ 已删除 {dir_name} 目录")
    
    for file_name in cleanup_files:
        if Path(file_name).exists():
            os.remove(file_name)
            print(f"✅ 已删除 {file_name} 文件")

def check_required_files():
    """检查必需的文件"""
    required_files = [
        'main.py',
        'cookie_exporter.py', 
        'excel_exporter.py',
        'requirements.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必需文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必需文件都存在")
    return True

def main():
    """主函数"""
    print("🚀 订单地址批量修改工具打包脚本")
    print("=" * 50)
    print(f"Python 版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print("=" * 50)
    
    # 检查必需文件
    if not check_required_files():
        print("请确保所有必需文件都在当前目录中")
        return
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("无法安装PyInstaller，请手动安装: pip install pyinstaller")
            return
    
    # 创建spec文件
    create_spec_file()
    
    # 构建EXE
    if build_exe():
        print("\n🎉 打包完成!")
        print("📁 EXE文件位置: dist/订单地址批量修改工具.exe")
        
        # 检查输出文件
        exe_path = Path("dist/订单地址批量修改工具.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"📊 文件大小: {size_mb:.1f} MB")
        
        print("\n💡 使用说明:")
        print("1. 运行 dist/订单地址批量修改工具.exe")
        print("2. 首次使用请点击'登录'按钮获取Cookie")
        print("3. 按顺序导入订单号和地址，然后开始修改")
        
        # 询问是否清理构建文件
        try:
            choice = input("\n是否清理构建文件? (y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                cleanup_build_files()
        except KeyboardInterrupt:
            print("\n用户取消操作")
    else:
        print("\n❌ 打包失败，请检查错误信息")

if __name__ == "__main__":
    main()
