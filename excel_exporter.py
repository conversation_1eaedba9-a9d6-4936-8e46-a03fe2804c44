#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel导出工具
解决订单号在Excel中显示为科学计数法的问题
"""

import json
from datetime import datetime
from pathlib import Path

def export_to_excel(orders_data, output_file=None):
    """导出数据到Excel文件"""
    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment, PatternFill
        
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "订单地址修改结果"
        
        # 设置表头
        headers = ['订单号', '地址', '目前状态']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="1E3A8A", end_color="1E3A8A", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # 写入数据
        for row, order in enumerate(orders_data, 2):
            # 订单号 - 设置为文本格式
            order_id_cell = ws.cell(row=row, column=1, value=order.get('order_id', ''))
            order_id_cell.number_format = '@'  # 文本格式
            order_id_cell.alignment = Alignment(horizontal="center")
            
            # 地址
            address_cell = ws.cell(row=row, column=2, value=order.get('address', ''))
            address_cell.alignment = Alignment(horizontal="left")
            
            # 状态
            status = order.get('status', '')
            status_cell = ws.cell(row=row, column=3, value=status)
            status_cell.alignment = Alignment(horizontal="center")
            
            # 根据状态设置颜色
            if "成功" in status:
                status_cell.fill = PatternFill(start_color="D4EDDA", end_color="D4EDDA", fill_type="solid")
            elif "失败" in status or "错误" in status:
                status_cell.fill = PatternFill(start_color="F8D7DA", end_color="F8D7DA", fill_type="solid")
        
        # 调整列宽
        ws.column_dimensions['A'].width = 20  # 订单号
        ws.column_dimensions['B'].width = 50  # 地址
        ws.column_dimensions['C'].width = 15  # 状态
        
        # 生成文件名
        if not output_file:
            timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
            output_file = f"订单地址修改结果_{timestamp}.xlsx"
        
        # 确保输出目录存在
        output_path = Path("exports") / output_file
        output_path.parent.mkdir(exist_ok=True)
        
        # 保存文件
        wb.save(output_path)
        
        return str(output_path)
        
    except ImportError:
        raise ImportError("需要安装openpyxl库：pip install openpyxl")
    except Exception as e:
        raise Exception(f"导出Excel文件时出错：{str(e)}")

def export_orders_data():
    """从orders_data.json导出Excel文件"""
    try:
        # 读取订单数据
        data_file = Path("data/orders_data.json")
        if not data_file.exists():
            print("❌ 未找到订单数据文件")
            return
        
        with open(data_file, 'r', encoding='utf-8') as f:
            orders_data = json.load(f)
        
        if not orders_data:
            print("❌ 订单数据为空")
            return
        
        # 导出到Excel
        output_file = export_to_excel(orders_data)
        print(f"✅ 成功导出到Excel文件：{output_file}")
        print("💡 订单号在Excel中会正确显示为文本格式，不会变成科学计数法")
        
    except Exception as e:
        print(f"❌ 导出失败：{e}")

if __name__ == "__main__":
    export_orders_data()
