# 更新日志

## 版本 1.3 (2025-08-01)

### 🎯 用户体验优化

1. **智能按钮状态管理**
   - ✅ 未粘贴订单号时，"导入地址"按钮自动禁用
   - ✅ 根据数据状态智能启用/禁用相关按钮
   - ✅ 修改过程中自动禁用不相关操作

2. **智能地址匹配**
   - ✅ 自动匹配地址数量与订单数量
   - ✅ 例：2个订单时，剪贴板有5个地址，只导入前2个
   - ✅ 提供清晰的导入反馈信息

3. **导出后自动清理**
   - ✅ 导出完成后自动删除Cookie导出工具文件夹
   - ✅ 自动删除data文件夹
   - ✅ 清空内存数据并重置登录状态
   - ✅ 自动更新按钮状态

4. **订单号显示问题修复**
   - ✅ 提供3种导出格式：Excel文件、CSV-Excel兼容、CSV-标准
   - ✅ Excel格式完美保持订单号格式
   - ✅ 自动降级处理（Excel库未安装时）

### 🔧 技术改进

1. **按钮状态逻辑优化**
   - 更精确的状态判断
   - 实时状态更新
   - 更好的用户引导

2. **文件清理机制**
   - 安全的文件夹删除
   - 完整的状态重置
   - 错误处理和日志记录

---

## 版本 1.2 (2025-08-01)

### 🐛 关键问题修复

1. **修复CSRF token大小写问题**
   - 修复了cookies中`KS-CSRF-Token`（大写）与代码中`ks-csrf-token`（小写）不匹配的问题
   - 修复了`merchantSessionKey`大小写不匹配的问题
   - 现在支持大小写不敏感的token提取

2. **完善API请求头**
   - 添加了所有必需的请求头字段
   - 包含`ktrace-str`、`trace-id`、`priority`等关键字段
   - 确保API调用完全符合服务器要求

### ✅ 验证结果

- ✅ CSRF token提取正常工作
- ✅ Merchant session key提取正常工作
- ✅ 程序启动和基本功能测试通过
- ✅ 解决了"[7210] csrf token invalid"错误的根本原因

---

## 版本 1.1 (2025-08-01)

### 🐛 问题修复

1. **修复CSRF token无效问题**
   - 程序现在会自动从cookies中提取最新的CSRF token
   - 自动提取merchant session key
   - 解决了"[7210] csrf token invalid"错误

2. **修复Unicode解码错误**
   - 修复了subprocess调用时的编码问题
   - 添加了错误忽略机制，提高程序稳定性

3. **改进错误处理**
   - 增强了网络请求的重试机制
   - 更好的错误信息显示
   - 完善的日志记录

### ✨ 功能改进

1. **动态token管理**
   - 自动从cookies中提取关键认证信息
   - 无需手动更新token值
   - 提高API调用成功率

2. **更好的用户体验**
   - 更清晰的错误提示
   - 实时状态更新
   - 自动数据保存

### 🔧 技术优化

1. **代码结构优化**
   - 分离了token提取逻辑
   - 改进了工作线程设计
   - 更好的异常处理

2. **性能提升**
   - 优化了网络请求
   - 减少了不必要的API调用
   - 改进了内存使用

---

## 版本 1.0 (2025-08-01)

### 🎉 初始版本

1. **核心功能**
   - 订单地址批量修改
   - 智能地址解析
   - 数据导入导出
   - 登录管理

2. **用户界面**
   - 现代化GUI设计
   - 实时状态显示
   - 直观的操作流程

3. **技术特性**
   - 异步处理
   - 数据持久化
   - 完整日志记录
   - 错误处理机制
